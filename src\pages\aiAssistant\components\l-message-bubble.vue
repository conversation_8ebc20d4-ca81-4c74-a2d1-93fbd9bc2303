<template>
  <div class="message-bubble" :class="{ 'user-bubble': isUser, 'status-message': statusMessage }" @click="handleClick">
    <!-- 文本消息 - 支持多种文本类型 -->
    <div v-if="isTextMessage" class="text-content" :class="{ collapsed: isCollapsed }">
      {{ content }}
      <!-- 流式输入时显示光标 -->
      <span v-if="streaming" class="streaming-cursor"></span>
      <div v-if="isCollapsed" class="expand-hint">点击展开</div>
    </div>
    <!-- 任务完成消息 -->
    <div v-else-if="type === 'task_complete'" class="task-complete-content">
      <div class="task-complete-header">
        <i class="fas fa-check-circle"></i>
        <span>任务执行完成</span>
      </div>

      <!-- 结构化显示任务完成信息 -->
      <div v-if="parsedTaskContent.isStructured" class="structured-task-content">
        {{ console.log('🎯 [DEBUG] 渲染结构化任务内容', parsedTaskContent) }}
        <!-- 任务概述 -->
        <div v-if="parsedTaskContent.overview" class="task-overview">
          {{ parsedTaskContent.overview }}
        </div>

        <!-- 步骤详情列表 -->
        <div v-if="parsedTaskContent.steps.length > 0" class="task-steps">
          <div v-for="(step, index) in parsedTaskContent.steps" :key="index" class="task-step-item">
            {{ step }}
          </div>
        </div>

        <!-- 操作统计 -->
        <div v-if="parsedTaskContent.summary" class="task-operation-summary">
          {{ parsedTaskContent.summary }}
        </div>
      </div>
    </div>
    <!-- 错误消息 -->
    <div v-else-if="type === 'error'" class="error-content">
      <div class="error-header">
        <i class="fas fa-exclamation-triangle"></i>
        <span>处理出错</span>
      </div>
      <div class="error-message">{{ content }}</div>
    </div>
    <!-- 音频消息 -->
    <div v-else-if="type === 'audio'" class="audio-content" @click="toggleAudioPlay">
      <div class="audio-icon">
        <i :class="['fas', isPlaying ? 'fa-pause' : 'fa-play']"></i>
      </div>
      <div class="audio-duration">{{ isPlaying ? formatRemainingTime() : formatDuration(audioDuration) }}</div>
      <div
        class="text-icon"
        :class="{
          transcribing: isTranscribing,
          clickable: transcribeResult,
          collapsed: !showTranscribeResult,
        }"
        @click="transcribeResult ? toggleTranscribeResult($event) : null"
      >
        文
      </div>
      <!-- 转写结果 -->
      <div v-if="transcribeResult && showTranscribeResult" class="transcribe-result">{{ transcribeResult }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'text', // 'text' 或 'audio'
  },
  audioUrl: {
    type: String,
    default: '',
  },
  audioDuration: {
    type: Number,
    default: 0, // 音频时长（毫秒）
  },
  isUser: {
    type: Boolean,
    default: false,
  },
  streaming: {
    type: Boolean,
    default: false,
  },
  isCollapsed: {
    type: Boolean,
    default: false,
  },
  onToggle: {
    type: Function,
    default: () => {
      /* no op */
    },
  },
  isTranscribing: {
    type: Boolean,
    default: false,
  },
  transcribeResult: {
    type: String,
    default: '',
  },
  statusMessage: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['toggle'])

// 计算属性：判断是否是文本类型消息
const isTextMessage = computed(() => {
  const textTypes = ['text', 'user', 'ai_streaming', 'ai_complete']
  return textTypes.includes(props.type)
})

// 计算属性：解析任务完成内容
const parsedTaskContent = computed(() => {
  console.log('🎯 [DEBUG] parsedTaskContent 计算属性被调用', {
    type: props.type,
    content: props.content,
    contentLength: props.content?.length,
    isTaskComplete: props.type === 'task_complete',
  })

  if (props.type !== 'task_complete' || !props.content) {
    console.log('🎯 [DEBUG] 不是任务完成消息或无内容，返回非结构化')
    return { isStructured: false, overview: '', steps: [], summary: '' }
  }

  const content = props.content.trim()

  // 检查是否包含结构化标识符（🎉、✅等）
  const hasStructuredMarkers = content.includes('🎉') || content.includes('✅')

  console.log('🎯 [DEBUG] 检查结构化标识符', {
    content,
    hasStructuredMarkers,
    'has🎉': content.includes('🎉'),
    'has✅': content.includes('✅'),
  })

  if (!hasStructuredMarkers) {
    console.log('🎯 [DEBUG] 无结构化标识符，尝试转换简单文本')

    // 临时方案：将简单的任务完成文本转换为结构化格式
    if (content.includes('任务执行完成') || content.includes('任务完成')) {
      console.log('🎯 [DEBUG] 检测到任务完成文本，转换为结构化格式')
      return {
        isStructured: true,
        overview: '🎉 ' + content,
        steps: ['✅ 任务已成功执行'],
        summary: '操作已完成',
      }
    }

    console.log('🎯 [DEBUG] 无法转换，返回非结构化')
    return { isStructured: false, overview: '', steps: [], summary: '' }
  }

  // 按行分割内容
  const lines = content
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line.length > 0)

  let overview = ''
  const steps = []
  let summary = ''

  for (const line of lines) {
    if (line.startsWith('🎉')) {
      // 任务概述（通常是第一行）
      overview = line
    } else if (line.startsWith('✅')) {
      // 步骤详情
      steps.push(line)
    } else if (line.includes('成功完成') || line.includes('个操作') || line.includes('个步骤')) {
      // 操作统计
      summary = line
    } else if (!overview && line.length > 0) {
      // 如果没有🎉标识，将第一个非空行作为概述
      overview = line
    }
  }

  const result = {
    isStructured: true,
    overview,
    steps,
    summary,
  }

  console.log('🎯 [DEBUG] 解析结果', {
    result,
    overviewLength: overview?.length,
    stepsCount: steps.length,
    summaryLength: summary?.length,
    lines: lines,
  })

  return result
})

// 音频播放状态
const isPlaying = ref(false)
let audioElement = null
let innerAudioContext = null

// 当前播放时间
const currentTime = ref(0)

// 添加转写结果显示状态
const showTranscribeResult = ref(true)

// 在组件挂载时创建音频元素
onMounted(() => {
  if (props.type === 'audio' && props.audioUrl) {
    // #ifdef H5
    audioElement = new Audio(props.audioUrl)

    // 监听播放结束事件
    audioElement.addEventListener('ended', handleAudioEnded)
    // 错误处理
    audioElement.addEventListener('error', handleAudioError)
    // 监听时间更新
    audioElement.addEventListener('timeupdate', handleTimeUpdate)
    // #endif

    // #ifndef H5
    // App 端使用 uni 的音频 API
    innerAudioContext = uni.createInnerAudioContext()
    innerAudioContext.src = props.audioUrl

    // 监听播放结束事件
    innerAudioContext.onEnded(() => {
      handleAudioEnded()
    })

    // 错误处理
    innerAudioContext.onError((res) => {
      console.error('音频播放错误：', res)
      handleAudioError(res)
    })

    // 监听时间更新
    innerAudioContext.onTimeUpdate(() => {
      currentTime.value = innerAudioContext.currentTime * 1000 // 转换为毫秒
    })
    // #endif
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  // #ifdef H5
  if (audioElement) {
    audioElement.pause()
    audioElement.removeEventListener('ended', handleAudioEnded)
    audioElement.removeEventListener('error', handleAudioError)
    audioElement.removeEventListener('timeupdate', handleTimeUpdate)
    audioElement = null
  }
  // #endif

  // #ifndef H5
  if (innerAudioContext) {
    innerAudioContext.stop()
    innerAudioContext.destroy()
    innerAudioContext = null
  }
  // #endif
})

// 处理时间更新
const handleTimeUpdate = () => {
  // #ifdef H5
  if (audioElement) {
    currentTime.value = audioElement.currentTime * 1000 // 转换为毫秒
  }
  // #endif
}

// 格式化剩余时间
const formatRemainingTime = () => {
  const remaining = Math.max(0, props.audioDuration - currentTime.value)
  const seconds = Math.ceil(remaining / 1000)
  return `${seconds}"`
}

// 切换音频播放/暂停
const toggleAudioPlay = () => {
  // #ifdef H5
  if (!audioElement) {
    console.error('音频元素不存在')
    return
  }

  if (isPlaying.value) {
    // 暂停播放
    audioElement.pause()
    isPlaying.value = false
  } else {
    // 播放前先停止所有其他正在播放的音频
    stopAllOtherAudio()

    // 开始播放
    audioElement.play().catch((error) => {
      console.error('播放音频失败：', error)
    })
    isPlaying.value = true
  }
  // #endif

  // #ifndef H5
  if (!innerAudioContext) {
    console.error('音频元素不存在')
    return
  }

  if (isPlaying.value) {
    // 暂停播放
    innerAudioContext.pause()
    isPlaying.value = false
  } else {
    // 播放前先停止所有其他正在播放的音频
    stopAllOtherAudio()

    // 开始播放
    innerAudioContext.play()
    isPlaying.value = true
  }
  // #endif
}

// 音频播放结束处理
const handleAudioEnded = () => {
  isPlaying.value = false
  currentTime.value = 0 // 重置当前时间
}

// 音频错误处理
const handleAudioError = (error) => {
  console.error('音频播放错误：', error)
  isPlaying.value = false
}

// 停止所有其他音频播放
const stopAllOtherAudio = () => {
  // #ifdef H5
  // 创建一个自定义事件，通知其他音频消息停止播放
  document.dispatchEvent(
    new CustomEvent('stop-all-audio', {
      detail: { except: props.audioUrl },
    })
  )
  // #endif

  // #ifndef H5
  // App 端使用 uni 的事件系统
  uni.$emit('stop-all-audio', { except: props.audioUrl })
  // #endif
}

// 监听全局音频停止事件
onMounted(() => {
  // #ifdef H5
  document.addEventListener('stop-all-audio', handleStopAllAudio)
  // #endif

  // #ifndef H5
  uni.$on('stop-all-audio', handleStopAllAudio)
  // #endif
})

onUnmounted(() => {
  // #ifdef H5
  document.removeEventListener('stop-all-audio', handleStopAllAudio)
  // #endif

  // #ifndef H5
  uni.$off('stop-all-audio', handleStopAllAudio)
  // #endif
})

// 处理停止所有音频的事件
const handleStopAllAudio = (event) => {
  // #ifdef H5
  // 如果当前音频不是例外，则停止播放
  if (audioElement && isPlaying.value && event.detail.except !== props.audioUrl) {
    audioElement.pause()
    isPlaying.value = false
  }
  // #endif

  // #ifndef H5
  // App 端处理
  if (innerAudioContext && isPlaying.value && event.except !== props.audioUrl) {
    innerAudioContext.pause()
    isPlaying.value = false
  }
  // #endif
}

// 格式化音频时长
const formatDuration = (duration) => {
  const seconds = Math.floor(duration / 1000)
  return `${seconds}"`
}

// 处理点击事件
const handleClick = () => {
  if (props.type === 'text' && !props.isUser) {
    props.onToggle()
  }
}

// 切换转写结果显示/隐藏
const toggleTranscribeResult = (event) => {
  event.stopPropagation() // 阻止事件冒泡，避免触发音频播放
  showTranscribeResult.value = !showTranscribeResult.value
}
</script>

<style lang="scss" scoped>
.message-bubble {
  max-width: 70%;
  border-radius: 12px;
  padding: 10px 15px;
  background-color: var(--color-white);
  color: var(--color-gray-800);
  word-break: break-word;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);

  &.user-bubble {
    background-color: var(--color-primary-light);
    color: white;
    border-top-right-radius: 4px;
    border: 1px solid transparent;
  }

  &:not(.user-bubble) {
    border-top-left-radius: 4px;
  }

  &.status-message {
    background-color: #f0f8ff;
    color: #666;
    border-color: #e0e8ef;
    font-style: italic;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--color-primary-light);
      margin-right: 5px;
      animation: status-pulse 1.2s infinite;
      vertical-align: middle;
    }
  }

  // 音频消息样式
  .audio-content {
    display: flex;
    align-items: center;
    min-width: 120px;
    cursor: pointer;
    justify-content: space-between;
    padding: 0 4px;
    position: relative;
    flex-wrap: wrap;

    .text-icon {
      font-size: 14px;
      color: var(--color-gray-600);
      margin-left: 8px;

      &.transcribing {
        animation: transcribing 1s infinite;
      }

      &.clickable {
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }

      &.collapsed {
        opacity: 0.6;
      }
    }

    .transcribe-result {
      width: 100%;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid var(--color-gray-200);
      font-size: 14px;
      color: var(--color-gray-700);
      word-break: break-all;
      cursor: default;
      white-space: pre-line;
    }

    .audio-icon {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: var(--color-gray-700);
    }

    .audio-duration {
      font-size: 14px;
      color: var(--color-gray-700);
      min-width: 36px;
      text-align: right;
    }
  }

  .text-content {
    word-break: break-all;
    white-space: pre-wrap;

    &.collapsed {
      max-height: 60px;
      overflow: hidden;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: linear-gradient(to bottom, transparent, #fff);
      }
    }

    .expand-hint {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      color: #999;
      font-size: 12px;
      padding: 4px 0;
    }
  }
}

.user-bubble .audio-content {
  .audio-icon {
    color: var(--color-primary-dark);
  }

  .audio-duration {
    color: var(--color-primary-dark);
  }
}

@keyframes transcribing {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.5;
    transform: scale(0.95);
  }
}

/* 流式输入光标动画 - 优化版本 */
.streaming-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background: linear-gradient(to bottom, var(--color-primary), var(--color-primary-light));
  // margin-left: 2px;
  border-radius: 1px;
  animation: cursor-pulse 1.2s ease-in-out infinite;
  vertical-align: text-bottom;
}

@keyframes cursor-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scaleY(1);
  }

  50% {
    opacity: 0.3;
    transform: scaleY(0.8);
  }
}

@keyframes status-pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 任务完成消息样式 */
.task-complete-content {
  .task-complete-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: var(--color-success);
    font-weight: 500;

    i {
      font-size: 16px;
    }
  }

  /* 结构化任务内容样式 */
  .structured-task-content {
    .task-overview {
      font-size: 16px;
      font-weight: 500;
      color: var(--color-gray-800);
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .task-steps {
      margin-bottom: 12px;

      .task-step-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 6px;
        line-height: 1.5;
        color: var(--color-gray-700);
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .task-operation-summary {
      padding: 8px 12px;
      background-color: var(--color-success-light);
      border-radius: 6px;
      font-size: 13px;
      color: var(--color-success-dark);
      font-weight: 500;
      text-align: center;
    }
  }
}

/* 错误消息样式 */
.error-content {
  .error-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: var(--color-error);
    font-weight: 500;

    i {
      font-size: 16px;
    }
  }

  .error-message {
    line-height: 1.5;
    color: var(--color-error-dark);
  }
}
</style>
