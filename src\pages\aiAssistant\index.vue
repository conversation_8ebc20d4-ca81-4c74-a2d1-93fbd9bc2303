<template>
  <view class="ai-chat-page">
    <l-message-list
      ref="messageListRef"
      :messages="messages"
      :ai-state="aiState"
      @task-confirm="handleTaskConfirm"
      @task-cancel="handleTaskCancel"
    />
    <view class="message-input-wrapper">
      <z-message-input
        v-model="inputValue"
        @send="handleSendMessageStream"
        @send-audio="handleSendAudio"
        placeholder="有什么想问的..."
        cloud-path="aiAssistant/"
        :disabled="aiState.isProcessing || aiState.streaming.active"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import LMessageList from './components/l-message-list.vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'

const inputValue = ref('')
const messageListRef = ref(null)

// 消息类型定义（需要在使用前定义）
const MESSAGE_TYPES = {
  USER: 'user', // 用户消息
  AI_STREAMING: 'ai_streaming', // AI 流式消息
  AI_COMPLETE: 'ai_complete', // AI 完成消息
  TASK_COMPLETE: 'task_complete', // 任务完成消息
  ERROR: 'error', // 错误消息
}

const MESSAGE_STATUS = {
  SENDING: 'sending', // 发送中
  STREAMING: 'streaming', // 流式显示中
  COMPLETE: 'complete', // 完成
  ERROR: 'error', // 错误
}

// SSE 消息类型定义（与后端保持一致）
const SSE_MESSAGE_TYPES = {
  PROCESSING_START: 'processing_start',
  INTENT_ANALYZING: 'intent_analyzing',
  INTENT_RECOGNIZED: 'intent_recognized',
  CHAT_RESPONSE_START: 'chat_response_start',
  CHAT_CONTENT_CHUNK: 'chat_content_chunk',
  CHAT_RESPONSE_END: 'chat_response_end',
  TASK_PREPARATION: 'task_preparation',
  EXECUTION_PLAN: 'execution_plan',
  EXECUTION_STEP: 'execution_step',
  STEP_RESULT: 'step_result',
  EXECUTION_COMPLETE: 'execution_complete',
  EXECUTION_FAILED: 'execution_failed',
  TASK_STEP_START: 'task_step_start',
  TASK_STEP_PROGRESS: 'task_step_progress',
  TASK_STEP_COMPLETE: 'task_step_complete',
  TASK_ALL_COMPLETE: 'task_all_complete',
  ERROR: 'error',
  SESSION_END: 'session_end',
}

const messages = ref([
  {
    _id: 'welcome_1',
    content:
      '你好！我是你的 AI 助手，有什么可以帮助你的吗？\n\n💡 测试建议：\n• 发送"创建一个测试任务"来测试任务执行流程\n• 发送"你好"来测试普通聊天功能',
    type: MESSAGE_TYPES.AI_COMPLETE,
    isUser: false,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  },
  // 示例：任务确认卡片消息（可以删除此示例）
  // {
  //   _id: '2',
  //   type: 'task-confirm',
  //   recognizedContent: '明天下午 2 点开会讨论项目进度',
  //   isUser: false,
  //   time: new Date().toISOString(),
  // },
])

const aiApi = uniCloud.importObject('ai', {
  customUI: true, // 取消自动展示的交互提示界面
})

// 全新的 AI 状态管理 - 前后端一体化重构
const aiState = ref({
  // 会话状态
  sessionId: null,
  isProcessing: false,

  // 加载状态
  loading: {
    show: false,
    text: '',
    stage: '', // thinking/analyzing/preparing/executing/completing
    progress: null,
  },

  // 流式消息状态
  streaming: {
    active: false,
    messageId: null,
    intentType: null,
  },
})

// 连接状态管理
const connectionStatus = ref('disconnected') // connecting/connected/error
const retryCount = ref(0)
const maxRetries = 3

// 状态控制函数
const showLoading = (text, stage = 'thinking') => {
  aiState.value.loading = {
    show: true,
    text,
    stage,
    progress: null,
  }
}

const updateLoading = (updates) => {
  if (aiState.value.loading.show) {
    Object.assign(aiState.value.loading, updates)
  }
}

const hideLoading = () => {
  aiState.value.loading.show = false
}

const resetAiState = () => {
  aiState.value = {
    sessionId: null,
    isProcessing: false,
    loading: {
      show: false,
      text: '',
      stage: '',
      progress: null,
    },
    streaming: {
      active: false,
      messageId: null,
      intentType: null,
    },
  }
}

onUnmounted(() => {
  // 组件卸载时重置状态
  resetAiState()
})

// 消息操作函数
const addUserMessage = (content) => {
  const userMessage = {
    _id: `user_${Date.now()}`,
    type: MESSAGE_TYPES.USER,
    content,
    isUser: true,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  }
  messages.value.push(userMessage)
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })
}

const createStreamingMessage = () => {
  const streamingMessage = {
    _id: `streaming_${Date.now()}`,
    type: MESSAGE_TYPES.AI_STREAMING,
    content: '',
    isUser: false,
    status: MESSAGE_STATUS.STREAMING,
    time: new Date().toISOString(),
  }
  messages.value.push(streamingMessage)
  aiState.value.streaming.active = true
  aiState.value.streaming.messageId = streamingMessage._id
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })
}

const appendStreamingContent = (content) => {
  if (aiState.value.streaming.messageId) {
    const messageIndex = messages.value.findIndex((msg) => msg._id === aiState.value.streaming.messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex].content += content
      nextTick(() => {
        messageListRef.value?.scrollToBottom()
      })
    }
  }
}

const finalizeStreamingMessage = () => {
  if (aiState.value.streaming.messageId) {
    const messageIndex = messages.value.findIndex((msg) => msg._id === aiState.value.streaming.messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex].status = MESSAGE_STATUS.COMPLETE
      messages.value[messageIndex].type = MESSAGE_TYPES.AI_COMPLETE
    }
  }
}

const addTaskCompleteMessage = (data) => {
  console.log('🎯 [DEBUG] addTaskCompleteMessage 被调用', {
    data,
    dataMessage: data?.message,
    dataSummary: data?.summary,
    hasMessage: !!data?.message,
    messageLength: data?.message?.length,
    hasStructuredMarkers: data?.message?.includes('🎉') || data?.message?.includes('✅'),
  })

  const taskMessage = {
    _id: `task_${Date.now()}`,
    type: MESSAGE_TYPES.TASK_COMPLETE,
    content: data.message,
    summary: data.summary,
    isUser: false,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  }

  console.log('🎯 [DEBUG] 创建的任务完成消息对象', {
    taskMessage,
    messageType: taskMessage.type,
    messageContent: taskMessage.content,
    contentHasMarkers: taskMessage.content?.includes('🎉') || taskMessage.content?.includes('✅'),
  })

  messages.value.push(taskMessage)

  console.log('🎯 [DEBUG] 消息已添加到列表', {
    totalMessages: messages.value.length,
    lastMessage: messages.value[messages.value.length - 1],
  })

  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })
}

const addErrorMessage = (error) => {
  const errorMessage = {
    _id: `error_${Date.now()}`,
    type: MESSAGE_TYPES.ERROR,
    content: `抱歉，处理过程中出现错误：${error}`,
    isUser: false,
    status: MESSAGE_STATUS.ERROR,
    time: new Date().toISOString(),
  }
  messages.value.push(errorMessage)
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })
}

// 添加任务确认消息的辅助函数（保持兼容性）
const addTaskConfirmMessage = (recognizedContent) => {
  const taskConfirmMessage = {
    _id: `task_confirm_${Date.now()}`,
    type: 'task-confirm',
    recognizedContent,
    isUser: false,
    time: new Date().toISOString(),
  }
  messages.value.push(taskConfirmMessage)
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })
}

// 全新的流式消息处理逻辑
const handleStreamMessage = (message) => {
  console.log('🔔 收到流式消息：', {
    type: message.type,
    hasData: !!message.data,
    hasSessionId: !!message.sessionId,
    messageKeys: Object.keys(message),
    fullMessage: message,
  })

  // 特别关注任务相关的消息
  if (message.type && (message.type.includes('task') || message.type.includes('complete'))) {
    console.log('🎯 [DEBUG] 任务相关消息详情', {
      type: message.type,
      data: message.data,
      dataKeys: message.data ? Object.keys(message.data) : [],
      dataMessage: message.data?.message,
      dataSummary: message.data?.summary,
    })
  }

  const { type, data, sessionId, timestamp } = message

  // 验证会话 ID（对于执行器推送的消息，可能没有 sessionId）
  if (aiState.value.sessionId && sessionId && sessionId !== aiState.value.sessionId) {
    console.warn('收到不匹配的会话消息，忽略', { expected: aiState.value.sessionId, received: sessionId })
    return
  }

  switch (type) {
    case SSE_MESSAGE_TYPES.PROCESSING_START:
      aiState.value.sessionId = sessionId
      aiState.value.isProcessing = true
      // 直接使用后端推送的消息内容
      showLoading(data.message, 'thinking')
      break

    case SSE_MESSAGE_TYPES.INTENT_ANALYZING:
      // 直接使用后端推送的消息内容
      updateLoading({
        text: data.message,
        stage: 'analyzing',
      })
      break

    case SSE_MESSAGE_TYPES.INTENT_RECOGNIZED:
      aiState.value.streaming.intentType = data.intentType
      // 直接使用后端推送的描述信息
      updateLoading({
        text: data.description,
        stage: data.intentType === 'chat' ? 'preparing' : 'executing',
      })
      break

    case SSE_MESSAGE_TYPES.CHAT_RESPONSE_START:
      // 聊天开始，准备流式显示，使用后端推送的消息
      updateLoading({
        text: data.message,
        stage: 'generating',
      })
      break

    case SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK:
      // 首次收到内容时，切换到流式显示
      if (!aiState.value.streaming.active) {
        hideLoading()
        createStreamingMessage()
      }
      appendStreamingContent(data.content)
      break

    case SSE_MESSAGE_TYPES.CHAT_RESPONSE_END:
      // 聊天回复完成
      finalizeStreamingMessage()
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.TASK_PREPARATION:
      // 直接使用后端推送的消息内容
      updateLoading({
        text: data.message,
        stage: 'preparing',
      })
      break

    case SSE_MESSAGE_TYPES.EXECUTION_PLAN:
      // 执行计划信息，适配后端实际的消息格式
      // 后端推送格式：{ type: 'execution_plan', plan: {...}, timestamp: ... }
      updateLoading({
        text: `准备执行 ${message.plan?.totalSteps || 0} 个步骤`,
        stage: 'preparing',
        progress: {
          current: 0,
          total: message.plan?.totalSteps || 0,
        },
      })
      break

    case SSE_MESSAGE_TYPES.EXECUTION_STEP:
      // 步骤开始执行，适配后端实际的消息格式
      // 后端推送格式：{ type: 'execution_step', step: {...}, timestamp: ... }
      updateLoading({
        text: message.step?.description || '执行步骤中...',
        stage: 'executing',
        progress: {
          current: 1, // 步骤开始
          total: null, // 这里无法获取总步骤数，因为消息格式中没有
        },
      })
      break

    case SSE_MESSAGE_TYPES.STEP_RESULT:
      // 步骤执行完成，适配后端实际的消息格式
      // 后端推送格式：{ type: 'step_result', stepId: '...', result: {...}, executionTime: ..., timestamp: ... }
      if (message.result?.errCode === null || message.result?.errCode === 0) {
        // 成功情况，可以显示成功信息或继续下一步
        updateLoading({
          text: '步骤执行成功，准备下一步...',
          stage: 'executing',
        })
      } else {
        // 失败情况，显示错误信息
        updateLoading({
          text: `步骤执行失败：${message.result?.errMsg || '未知错误'}`,
          stage: 'error',
        })
      }
      break

    case SSE_MESSAGE_TYPES.EXECUTION_COMPLETE:
      // 执行完成，适配后端实际的消息格式
      // 后端推送格式：{ type: 'execution_complete', plan: {...}, summary: {...}, timestamp: ... }
      updateLoading({
        text: '任务执行完成',
        stage: 'completing',
      })
      break

    case SSE_MESSAGE_TYPES.EXECUTION_FAILED:
      // 执行失败，适配后端实际的消息格式
      // 后端推送格式：{ type: 'execution_failed', plan: {...}, error: '...', timestamp: ... }
      hideLoading()
      addErrorMessage(message.error || '任务执行失败')
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.TASK_STEP_START:
      // 使用后端推送的步骤描述信息
      updateLoading({
        text: data.step?.description || data.message || '执行步骤中...',
        stage: 'executing',
        progress: data.step
          ? {
              current: 1, // 步骤开始时设为当前步骤
              total: data.totalSteps || null,
            }
          : null,
      })
      break

    case SSE_MESSAGE_TYPES.TASK_STEP_PROGRESS:
      // 更新进度信息，保持当前显示文本不变
      updateLoading({
        progress: {
          current: data.stepIndex,
          total: data.totalSteps,
          percent: data.progress,
        },
      })
      break

    case SSE_MESSAGE_TYPES.TASK_STEP_COMPLETE:
      // 步骤完成，继续显示加载状态等待下一步骤
      // 如果后端推送了完成消息，可以更新显示文本
      if (data.message) {
        updateLoading({
          text: data.message,
        })
      }
      break

    case SSE_MESSAGE_TYPES.TASK_ALL_COMPLETE:
      console.log('🎯 [DEBUG] 收到 TASK_ALL_COMPLETE 消息', {
        type,
        data,
        sessionId,
        timestamp,
        fullMessage: message,
      })
      hideLoading()
      addTaskCompleteMessage(data)
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.ERROR:
      hideLoading()
      // 直接使用后端推送的错误信息
      addErrorMessage(data.error)
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.SESSION_END:
      resetAiState()
      break

    default:
      console.warn('❌ 未知的消息类型：', {
        type,
        expectedTypes: Object.values(SSE_MESSAGE_TYPES),
        fullMessage: message,
        isTypeInExpected: Object.values(SSE_MESSAGE_TYPES).includes(type),
      })
  }
}

// 使用复用的 SSE Channel 进行流式聊天
const handleSendMessageStreamWithRetry = async (userMessage, historyMessages, attempt = 1) => {
  let timeoutId = null

  try {
    connectionStatus.value = 'connecting'

    const channel = new uniCloud.SSEChannel()

    // 设置 5 分钟超时
    timeoutId = setTimeout(() => {
      console.log('流式聊天超时，5 分钟未收到响应')
      handleStreamMessage({
        type: SSE_MESSAGE_TYPES.ERROR,
        data: { error: '请求超时（5 分钟），请重新尝试' },
      })
      channel.close()
    }, 300000)

    channel.on('open', () => {
      console.log('SSE Channel 连接成功')
      connectionStatus.value = 'connected'
      retryCount.value = 0
    })

    channel.on('message', (data) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      handleStreamMessage(data)
    })

    channel.on('end', (data) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      handleStreamMessage(data)
    })

    channel.on('error', (error) => {
      console.error('SSE Channel 错误：', error)
      connectionStatus.value = 'error'

      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }

      if (attempt < maxRetries) {
        console.log(`连接失败，准备第${attempt + 1}次重试...`)
        setTimeout(() => {
          handleSendMessageStreamWithRetry(userMessage, historyMessages, attempt + 1)
        }, 1000 * attempt)
      } else {
        handleStreamMessage({
          type: SSE_MESSAGE_TYPES.ERROR,
          data: { error: `连接失败，已重试${maxRetries}次：${error.message || '连接错误'}` },
        })
      }
    })

    channel.on('close', () => {
      console.log('SSE Channel 连接关闭')
      if (connectionStatus.value !== 'error') {
        connectionStatus.value = 'disconnected'
      }
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    })

    await channel.open()
    console.log('SSE Channel 已开启')

    const response = await aiApi.chatStreamSSE({
      message: userMessage,
      messages: historyMessages,
      channel: channel,
    })

    console.log('流式聊天接口调用结果：', response)

    if (response.errCode !== 0) {
      throw new Error(response.errMsg || '调用 AI 接口失败')
    }
  } catch (error) {
    console.error(`流式聊天失败（第${attempt}次尝试）：`, error)
    connectionStatus.value = 'error'

    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }

    if (attempt < maxRetries) {
      console.log(`接口调用失败，准备第${attempt + 1}次重试...`)
      retryCount.value = attempt
      setTimeout(() => {
        handleSendMessageStreamWithRetry(userMessage, historyMessages, attempt + 1)
      }, 1000 * attempt)
    } else {
      handleStreamMessage({
        type: SSE_MESSAGE_TYPES.ERROR,
        data: { error: `请求失败，已重试${maxRetries}次：${error.message || '网络连接异常，请检查网络后重试'}` },
      })
    }
  }
}

// 全新的发送消息流程
const handleSendMessageStream = async () => {
  if (!inputValue.value.trim() || aiState.value.isProcessing) return

  // 1. 添加用户消息
  addUserMessage(inputValue.value)

  // 2. 清空输入框
  const userMessage = inputValue.value
  inputValue.value = ''

  // 3. 滚动到底部
  nextTick(() => {
    messageListRef.value?.scrollToBottom()
  })

  // 4. 发送请求
  await sendMessageToAI(userMessage)
}

// 发送消息到 AI 的核心函数
const sendMessageToAI = async (userMessage) => {
  try {
    const channel = new uniCloud.SSEChannel()

    channel.on('open', () => {
      console.log('SSE 连接已建立')
      connectionStatus.value = 'connected'
    })

    channel.on('message', (data) => {
      handleStreamMessage(data)
    })

    channel.on('end', (data) => {
      if (data) {
        handleStreamMessage(data)
      }
      connectionStatus.value = 'disconnected'
    })

    channel.on('error', (error) => {
      console.error('SSE 连接错误：', error)
      connectionStatus.value = 'error'
      handleStreamMessage({
        type: SSE_MESSAGE_TYPES.ERROR,
        data: { error: error.message || '连接错误' },
      })
    })

    await channel.open()

    const historyMessages = messages.value
      .filter((msg) => msg.type === MESSAGE_TYPES.USER || msg.type === MESSAGE_TYPES.AI_COMPLETE)
      .map((msg) => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.content,
      }))

    const response = await aiApi.chatStreamSSE({
      message: userMessage,
      messages: historyMessages,
      channel: channel,
    })

    if (response.errCode !== 0) {
      throw new Error(response.errMsg || '调用 AI 接口失败')
    }
  } catch (error) {
    console.error('发送消息失败：', error)
    connectionStatus.value = 'error'
    handleStreamMessage({
      type: SSE_MESSAGE_TYPES.ERROR,
      data: { error: error.message },
    })
  }
}

const handleSendAudio = (audioData) => {
  // 添加音频消息
  const audioMessage = {
    _id: `audio_${Date.now()}`,
    type: 'audio',
    isUser: true,
    audioUrl: audioData.tempFileURL,
    audioDuration: audioData.duration,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  }
  messages.value.push(audioMessage)

  // 显示加载状态 - 注意：这里是临时的模拟实现
  // 实际应该调用后端语音处理接口，并通过 SSE 接收状态更新
  showLoading('处理语音中...', 'thinking')

  setTimeout(() => {
    hideLoading()
    const responseMessage = {
      _id: `response_${Date.now()}`,
      content: '我收到了你的语音，正在思考如何回复...',
      type: MESSAGE_TYPES.AI_COMPLETE,
      isUser: false,
      status: MESSAGE_STATUS.COMPLETE,
      time: new Date().toISOString(),
    }
    messages.value.push(responseMessage)
    nextTick(() => {
      messageListRef.value?.scrollToBottom()
    })
  }, 1500)
}

// 任务确认处理函数
const handleTaskConfirm = ({ messageId, content }) => {
  console.log('任务确认：', { messageId, content })

  // 找到对应的消息并转换为普通文本消息
  const messageIndex = messages.value.findIndex((msg) => msg._id === messageId)
  if (messageIndex !== -1) {
    messages.value[messageIndex] = {
      ...messages.value[messageIndex],
      type: 'text',
      content: `任务已确认：${content}`,
      isUser: false,
      time: new Date().toISOString(),
    }
  }

  // 这里可以添加确认后的处理逻辑，比如实际创建任务
}

const handleTaskCancel = ({ messageId }) => {
  console.log('任务取消：', messageId)

  // 找到对应的消息并转换为普通文本消息
  const messageIndex = messages.value.findIndex((msg) => msg._id === messageId)
  if (messageIndex !== -1) {
    messages.value[messageIndex] = {
      ...messages.value[messageIndex],
      type: 'text',
      content: '任务已取消',
      isUser: false,
      time: new Date().toISOString(),
    }
  }

  // 这里可以添加取消后的处理逻辑
}
</script>

<style lang="scss" scoped>
.ai-chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f5f7;
}

.message-input-wrapper {
  background-color: #fff;
}

.channel-status-indicator {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  padding: 8px 16px;

  .status-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: pulse 1.5s infinite;
    }

    .status-text {
      color: #fff;
      font-size: 12px;
    }

    &.connecting .status-dot {
      background-color: #ffa500;
    }

    &.error .status-dot {
      background-color: #ff4757;
    }

    &.disconnected .status-dot {
      background-color: #747d8c;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}
</style>
